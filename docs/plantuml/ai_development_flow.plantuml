@startuml ai_development_flow

title PathForge AI - Complete AI Development Workflow

' Define colors
skinparam rectangle {
  BackgroundColor #F8F9FA
  BorderColor #6C757D
}

skinparam package {
  BackgroundColor #E3F2FD
  BorderColor #1976D2
}

' AI Development Tools
package "AI Development Tools" {
  rectangle "Code Editors" {
    [Cursor]
    [Augment] 
    [GitHub Copilot]
    [VS Code]
  }
  
  rectangle "AI Assistants" {
    [Claude]
    [ChatGPT]
    [Gemini]
  }
  
  rectangle "Memory & Context" {
    [MCP Server]
    [Mem0]
    [memory.json]
  }
  
  note right of [Memory & Context]
    **Shared Memory System**
    - MCP server for real-time sharing
    - Mem0 for persistent context
    - Common instructions for consistency
    - Shared only between Code Editors
  end note
}

' Development Workflow
package "Development Workflow" {
  rectangle "1. Requirements" {
    [CustomGPT Agent]
    [Create Requirements]
    [Store in requirements/]
  }
  
  note right of [1. Requirements]
    **CustomGPT Agent Requirements**
    - Generated by BA team
    - Shared via CustomGPT agent
    - Standardized format
  end note
  
  rectangle "2. Design" {
    [GitHub Copilot + Agent Mode]
    [Claude Sonnet 4]
    [Software Architecture]
    [Agent Design]
    [Store in designs/]
  }
  
  rectangle "3. Task Management" {
    [Break Down Tasks]
    [Sprint Planning]
    [Store in tasks/]
  }
  
  rectangle "4. Implementation" {
    [Frontend (React)]
    [Backend (FastAPI)]
    [AI Agents (LangGraph)]
    [Streamlit UI]
  }
  
  note right of [4. Implementation]
    **AI-Enhanced Implementation**
    Input: Task + Requirement + Design + Docs
    AI tools provide context-aware assistance
    Real-time code generation and debugging
  end note
  
  rectangle "5. Code Review" {
    [GitHub Copilot Review]
    [SonarQube]
    [Peer Review]
    [Quality Gates]
  }
  
  note right of [5. Code Review]
    **Multi-layered Code Review**
    - AI-powered review (GitHub Copilot)
    - Static analysis (SonarQube)
    - Human peer review
    - Automated quality gates
  end note
  
  rectangle "6. CI/CD" {
    [Build / Run Test]
    [Build Docker Image]
    [Push Docker Image]
    [Send Portainer Webhook]
  }
  
  note right of [6. CI/CD]
    **CI Pipeline Steps**
    1. Build & Run Tests
    2. Build Docker Image
    3. Push to Registry
    4. Trigger Portainer Deployment
  end note
}

' Documentation System
package "Documentation & Knowledge" {
  rectangle "Documentation Files" {
    [requirements/]
    [designs/]
    [tasks/]
    [langgraph_docs/]
    [plantuml/]
  }
  
  rectangle "Knowledge Sharing" {
    [Meeting Notes]
    [Best Practices]
    [AI Guidelines]
  }
}

' Key Relationships
[CustomGPT Agent] --> [Create Requirements] : "BA team shared requirements"
[Create Requirements] --> [Software Architecture] : "Requirements drive design"
[GitHub Copilot + Agent Mode] --> [Software Architecture] : "AI-assisted design"
[Claude Sonnet 4] --> [Agent Design] : "Agent design assistance"
[Software Architecture] --> [Break Down Tasks] : "Design creates tasks"
[Break Down Tasks] --> [Frontend (React)] : "Tasks drive implementation"
[Break Down Tasks] --> [Backend (FastAPI)] : "Tasks drive implementation"
[Break Down Tasks] --> [AI Agents (LangGraph)] : "Tasks drive implementation"
[Break Down Tasks] --> [Streamlit UI] : "Tasks drive implementation"

' Implementation to Code Review - single arrow from group to group
"4. Implementation" --> "5. Code Review" : "Code review process"

' Documentation & Knowledge as input to AI tools and implementation
"Documentation & Knowledge" --> "AI Development Tools" : "Context for prompts"
"Documentation & Knowledge" --> "4. Implementation" : "Reference docs & specs"

' Code Review to CI/CD pipeline
[GitHub Copilot Review] --> [Build / Run Test] : "CI pipeline"
[SonarQube] --> [Build / Run Test] : "Quality gate passed"
[Peer Review] --> [Build / Run Test] : "Review approved"
[Quality Gates] --> [Build / Run Test] : "Quality approved"

' CI/CD Pipeline sequence
[Build / Run Test] --> [Build Docker Image] : "Tests passed"
[Build Docker Image] --> [Push Docker Image] : "Image built"
[Push Docker Image] --> [Send Portainer Webhook] : "Deploy trigger"

' Memory & Context shared with Code Editors group only
[Memory & Context] ..> "Code Editors" : "Shared context"

' AI Assistants are standalone (no connections to other groups)

[AI Agents (LangGraph)] ..> [langgraph_docs/] : "Reference documentation"

@enduml
