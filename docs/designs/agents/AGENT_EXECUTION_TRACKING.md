# Agent Execution Tracking System

This document describes the comprehensive agent execution tracking system implemented for the PathForge AI platform. The system provides real-time monitoring, logging, and visualization of agent workflows, tool calls, and sub-agent delegations.

## Overview

The tracking system consists of several key components:

1. **Execution Logger** - Core logging system for capturing execution events
2. **Tracking Schemas** - Data structures for execution events and traces
3. **Streamlit UI Components** - Real-time visualization and filtering
4. **Agent Integration** - Enhanced logging in PathForge supervisor agent
5. **Service Integration** - Streaming tracking events through the API

## Core Components

### 1. Execution Logger (`src/agents/execution_logger.py`)

The `ExecutionLogger` class provides centralized logging for agent execution events:

```python
from agents.execution_logger import execution_logger

# Start a new execution trace
trace = execution_logger.start_trace(
    trace_id="workflow-123",
    thread_id="thread-456",
    run_id="run-789",
    agent_name="pathforge-supervisor"
)

# Log individual events
execution_logger.log_event(
    trace_id="workflow-123",
    event_type=ExecutionEventType.TOOL_CALL,
    agent_name="pathforge-supervisor",
    status=ExecutionStatus.STARTED,
    thread_id="thread-456",
    run_id="run-789",
    tool_name="skill_sync_agent_tool",
    input_data={"user_id": "user123"}
)

# Track tool calls with context manager
with execution_logger.track_tool_call(
    trace_id="workflow-123",
    tool_name="skill_sync_agent_tool",
    agent_name="pathforge-supervisor",
    thread_id="thread-456",
    run_id="run-789"
) as event_id:
    # Tool execution code here
    result = await some_tool.invoke(params)
```

### 2. Tracking Schemas (`src/schema/tracking.py`)

Defines the data structures for tracking:

- **ExecutionEvent** - Individual execution events (tool calls, status updates, errors)
- **AgentExecutionTrace** - Complete trace of an agent workflow
- **ExecutionStatus** - Event status enumeration (STARTED, SUCCESS, ERROR, etc.)
- **ExecutionEventType** - Event type enumeration (TOOL_CALL, SUB_AGENT_CALL, etc.)
- **TrackingFilter** - Filter criteria for display
- **TrackingDisplayConfig** - UI display configuration

### 3. Streamlit UI (`src/streamlit_tracking.py`)

Provides real-time tracking visualization:

```python
from streamlit_tracking import render_agent_execution_tracking

# Render tracking interface
render_agent_execution_tracking(thread_id="current-thread")
```

**Features:**
- Real-time event display with color-coded status indicators
- Filtering by agent, event type, status, and time range
- Search functionality across event data
- Collapsible event details with input/output data
- Execution statistics and summaries
- Configurable display options

### 4. Agent Integration

The PathForge supervisor agent has been enhanced with comprehensive logging:

```python
# Automatic logging in supervisor nodes
async def skill_sync_node(state: SupervisorState, config: RunnableConfig):
    # Get logging context
    thread_id = config.get("configurable", {}).get("thread_id", "unknown")
    run_id = str(config.get("run_id", "unknown"))
    trace_id = f"pathforge-{state['workflow_state'].session_id}"

    # Log sub-agent call start
    execution_logger.log_event(
        trace_id=trace_id,
        event_type=ExecutionEventType.SUB_AGENT_CALL,
        agent_name="pathforge-supervisor",
        status=ExecutionStatus.STARTED,
        thread_id=thread_id,
        run_id=run_id,
        tool_name="skill_sync_agent_tool",
        input_data={"user_id": user_id, "sync_sources": sync_sources}
    )

    # Execute tool
    result = await skill_sync_agent_tool.ainvoke(params)

    # Log result
    execution_logger.log_event(
        trace_id=trace_id,
        event_type=ExecutionEventType.SUB_AGENT_RESULT,
        agent_name="pathforge-supervisor",
        status=ExecutionStatus.SUCCESS if result["success"] else ExecutionStatus.ERROR,
        thread_id=thread_id,
        run_id=run_id,
        tool_name="skill_sync_agent_tool",
        output_data=result
    )
```

## Event Types

The system tracks the following event types:

- **AGENT_START** - Agent workflow initialization
- **AGENT_END** - Agent workflow completion
- **TOOL_CALL** - Tool invocation start
- **TOOL_RESULT** - Tool execution result
- **SUB_AGENT_CALL** - Sub-agent delegation start
- **SUB_AGENT_RESULT** - Sub-agent execution result
- **STATUS_UPDATE** - Workflow status changes
- **ERROR** - Error events

## Status Types

Events can have the following statuses:

- **STARTED** - Event initiated
- **IN_PROGRESS** - Event in progress
- **SUCCESS** - Event completed successfully
- **ERROR** - Event failed with error
- **COMPLETED** - Event finished (general completion)

## UI Features

### Real-time Tracking Display

The Streamlit interface provides:

1. **Live Event Stream** - Real-time display of execution events
2. **Status Indicators** - Color-coded status (🟢 success, 🔴 error, 🟡 in-progress)
3. **Event Hierarchy** - Visual representation of sub-agent calls and tool chains
4. **Timing Information** - Timestamps and duration metrics
5. **Data Inspection** - Expandable input/output data for each event

### Filtering and Search

- **Agent Filter** - Show events from specific agents
- **Event Type Filter** - Filter by event types (tool calls, errors, etc.)
- **Status Filter** - Filter by execution status
- **Time Range Filter** - Show events from specific time periods
- **Text Search** - Search across event data, tool names, and error messages

### Display Configuration

- **Show/Hide Timestamps** - Toggle timestamp display
- **Show/Hide Durations** - Toggle duration metrics
- **Show/Hide Input/Output Data** - Control data visibility
- **Event Limit** - Configure maximum events displayed
- **Auto-refresh** - Real-time updates

## Integration with Streamlit App

The tracking UI is integrated into the main Streamlit app and appears in the sidebar when using the PathForge supervisor agent:

```python
# In streamlit_app.py
if agent_client.agent == "pathforge-supervisor":
    st.divider()
    with st.expander("🔍 Agent Execution Tracking", expanded=False):
        render_agent_execution_tracking(thread_id=st.session_state.thread_id)
```

## Testing

### Unit Tests

Run the execution logger tests:

```bash
pytest tests/agents/test_execution_logger.py -v
```

### Demo Script

Test the tracking system with the demo script:

```bash
python tests/agents/demo_tracking_pathforge.py
```

Choose from:
1. **Full PathForge workflow** - Run a complete career development workflow with tracking
2. **Generate sample data** - Create sample tracking data for UI testing

## Performance Considerations

- **Memory Management** - Traces are stored in memory; use `clear_traces()` for cleanup
- **Event Listeners** - Minimal overhead for real-time notifications
- **UI Updates** - Configurable refresh intervals to balance responsiveness and performance
- **Data Filtering** - Client-side filtering for responsive UI

## Future Enhancements

Potential improvements for the tracking system:

1. **Persistent Storage** - Store traces in database for historical analysis
2. **Export Functionality** - Export traces to JSON/CSV for external analysis
3. **Performance Metrics** - Add more detailed performance monitoring
4. **Alert System** - Notifications for errors or performance issues
5. **Trace Visualization** - Graphical workflow visualization
6. **Integration with LangSmith** - Enhanced tracing integration

## Troubleshooting

### Common Issues

1. **No tracking data visible**
   - Ensure you're using the PathForge supervisor agent
   - Check that the thread_id matches between agent execution and UI

2. **Events not updating in real-time**
   - Verify auto-refresh is enabled in display settings
   - Check browser console for JavaScript errors

3. **Performance issues with large traces**
   - Reduce the maximum events displayed
   - Use time range filters to limit data
   - Clear old traces periodically

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger("agents.execution_logger").setLevel(logging.DEBUG)
```

## API Reference

### ExecutionLogger Methods

- `start_trace(trace_id, thread_id, run_id, agent_name)` - Start new trace
- `end_trace(trace_id, status, error_message)` - End trace
- `log_event(...)` - Log individual event
- `track_tool_call(...)` - Context manager for tool tracking
- `get_trace(trace_id)` - Retrieve specific trace
- `get_all_traces()` - Get all traces
- `get_traces_for_thread(thread_id)` - Get traces for thread
- `clear_traces(older_than_hours)` - Clear traces
- `add_listener(callback)` - Add event listener
- `remove_listener(callback)` - Remove event listener

### Event Listener Callback

```python
def event_callback(event: ExecutionEvent):
    """Handle execution events."""
    print(f"Event: {event.event_type} - {event.status}")
```

This tracking system provides comprehensive visibility into agent execution, making it easier to debug, monitor, and optimize agent workflows.
