import { UnstyledButton, Group, Text, Tooltip } from '@mantine/core';
import { Link } from 'react-router-dom';
import classes from './NavigationItem.module.css';

interface NavigationItemProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  href: string;
  active?: boolean;
  collapsed?: boolean;
}

export function NavigationItem({ icon: Icon, label, href, active, collapsed = false }: NavigationItemProps) {
  const button = (
    <UnstyledButton
      component={Link}
      to={href}
      className={`${classes.item} ${active ? classes.active : ''} ${collapsed ? classes.collapsed : ''}`}
    >
      <Group gap="sm" justify={collapsed ? 'center' : 'flex-start'}>
        <Icon size={20} className={classes.icon} />
        {!collapsed && (
          <Text size="sm" fw={500} className={classes.label}>
            {label}
          </Text>
        )}
      </Group>
    </UnstyledButton>
  );

  if (collapsed) {
    return (
      <Tooltip label={label} position="right" withArrow>
        {button}
      </Tooltip>
    );
  }

  return button;
}
