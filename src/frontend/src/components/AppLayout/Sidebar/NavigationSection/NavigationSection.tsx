import { Stack } from '@mantine/core';
import {
  IconHome,
  IconChartLine
} from '@tabler/icons-react';
import { useLocation } from 'react-router-dom';
import { NavigationItem } from './NavigationItem/NavigationItem';
import { useSidebar } from '../SidebarContext';

const navigationItems = [
  {
    icon: IconHome,
    label: 'Home',
    href: '/',
  },
  {
    icon: IconChartLine,
    label: 'Progress',
    href: '/progress',
  },
];

export function NavigationSection() {
  const location = useLocation();
  const { isCollapsed } = useSidebar();

  return (
    <Stack gap="xs">
      {navigationItems.map((item) => (
        <NavigationItem
          key={item.href}
          icon={item.icon}
          label={item.label}
          href={item.href}
          active={location.pathname === item.href}
          collapsed={isCollapsed}
        />
      ))}
    </Stack>
  );
}
