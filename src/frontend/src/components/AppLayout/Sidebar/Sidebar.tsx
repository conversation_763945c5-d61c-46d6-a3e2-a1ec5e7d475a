import { ScrollArea, ActionIcon, Tooltip } from '@mantine/core';
import { IconMenu2, IconX } from '@tabler/icons-react';
import { NavigationSection } from './NavigationSection/NavigationSection';
import { GoalHistorySection } from './GoalHistorySection/GoalHistorySection';
import { SkillsSection } from './SkillsSection/SkillsSection';
import { SettingsSection } from './SettingsSection/SettingsSection';
import { useSidebar } from './SidebarContext';
import classes from './Sidebar.module.css';

export function Sidebar() {
  const { isCollapsed, toggle } = useSidebar();

  return (
    <ScrollArea className={`${classes.sidebar} ${isCollapsed ? classes.collapsed : ''}`} h="100vh">
      <div className={classes.content}>
        {/* Toggle Button */}
        <div className={classes.toggleSection}>
          <Tooltip
            label={isCollapsed ? "Expand sidebar (Ctrl+B)" : "Collapse sidebar (Ctrl+B)"}
            position={isCollapsed ? "right" : "bottom"}
            withArrow
          >
            <ActionIcon
              variant="subtle"
              size="lg"
              onClick={toggle}
              className={classes.toggleButton}
              aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {isCollapsed ? <IconMenu2 size={20} /> : <IconX size={20} />}
            </ActionIcon>
          </Tooltip>
        </div>

        {/* Navigation Section - Always visible */}
        <div className={classes.section}>
          <NavigationSection />
        </div>

        {/* Goal History Section - Hidden when collapsed */}
        {!isCollapsed && (
          <div className={classes.section}>
            <GoalHistorySection />
          </div>
        )}

        {/* Skills Section - Hidden when collapsed */}
        {!isCollapsed && (
          <div className={classes.section}>
            <SkillsSection />
          </div>
        )}

        {/* Settings Section - Always visible */}
        <div className={classes.section}>
          <SettingsSection />
        </div>
      </div>
    </ScrollArea>
  );
}
